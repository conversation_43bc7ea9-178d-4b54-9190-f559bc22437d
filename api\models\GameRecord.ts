/**
 * Game Record model for 工位摸鱼生存战
 */
import mongoose, { Document, Schema } from 'mongoose';

export interface IGameRecord extends Document {
  userId: mongoose.Types.ObjectId;
  gameType: 'survival' | 'expression' | 'quiz';
  score: number;
  duration: number; // 游戏时长（秒）
  level: number;
  achievements: string[];
  gameData: {
    correctAnswers?: number;
    totalQuestions?: number;
    expressionsUsed?: string[];
    survivalTime?: number;
    powerUpsUsed?: string[];
  };
  createdAt: Date;
}

const gameRecordSchema = new Schema<IGameRecord>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  gameType: {
    type: String,
    enum: ['survival', 'expression', 'quiz'],
    required: true
  },
  score: {
    type: Number,
    required: true,
    min: 0
  },
  duration: {
    type: Number,
    required: true,
    min: 0
  },
  level: {
    type: Number,
    default: 1,
    min: 1
  },
  achievements: [{
    type: String
  }],
  gameData: {
    correctAnswers: {
      type: Number,
      min: 0
    },
    totalQuestions: {
      type: Number,
      min: 0
    },
    expressionsUsed: [{
      type: String
    }],
    survivalTime: {
      type: Number,
      min: 0
    },
    powerUpsUsed: [{
      type: String
    }]
  }
}, {
  timestamps: true
});

// 索引优化
gameRecordSchema.index({ userId: 1, createdAt: -1 });
gameRecordSchema.index({ gameType: 1, score: -1 });

export default mongoose.model<IGameRecord>('GameRecord', gameRecordSchema);