/**
 * Footer component with copyright and links
 */
import { Heart } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">姜</span>
              </div>
              <span className="text-xl font-bold text-gray-900">AI摸鱼搭子</span>
            </div>
            <p className="text-gray-600 text-sm mb-4 max-w-md">
              让工作更有趣，让摸鱼更智能。打工姜君陪你度过每一个工作日，用AI的力量让职场生活充满乐趣。
            </p>
            <div className="flex items-center space-x-1 text-sm text-gray-500">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-500 fill-current" />
              <span>by 打工姜君团队</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 mb-4">快速链接</h3>
            <ul className="space-y-2">
              <li>
                <a href="/features" className="text-sm text-gray-600 hover:text-ginger-600 transition-colors">
                  功能详解
                </a>
              </li>
              <li>
                <a href="/story" className="text-sm text-gray-600 hover:text-ginger-600 transition-colors">
                  打工姜君的故事
                </a>
              </li>
              <li>
                <a href="/game" className="text-sm text-gray-600 hover:text-ginger-600 transition-colors">
                  摸个好鱼!
                </a>
              </li>
              <li>
                <a href="/community" className="text-sm text-gray-600 hover:text-orange-600 transition-colors">
                  加入我们
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 mb-4">支持</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-sm text-gray-600 hover:text-ginger-600 transition-colors">
                  帮助中心
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-gray-600 hover:text-orange-600 transition-colors">
                  用户协议
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-gray-600 hover:text-ginger-600 transition-colors">
                  隐私政策
                </a>
              </li>
              <li>
                <a href="#" className="text-sm text-gray-600 hover:text-ginger-600 transition-colors">
                  联系我们
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-gray-200 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-500">
              © 2024 AI摸鱼搭子. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-gray-500 transition-colors">
                <span className="sr-only">微信</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.476-4.991-5.748-6.364C12.691 2.612 10.656 2.188 8.691 2.188z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-500 transition-colors">
                <span className="sr-only">微博</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9.586 8.414a.5.5 0 0 1 .707 0L12 10.121l1.707-1.707a.5.5 0 0 1 .707.707L12.707 11l1.707 1.707a.5.5 0 0 1-.707.707L12 11.707l-1.707 1.707a.5.5 0 0 1-.707-.707L11.293 11 9.586 9.293a.5.5 0 0 1 0-.707z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;