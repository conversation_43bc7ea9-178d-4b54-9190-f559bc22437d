/**
 * Community page - User interaction and community features
 */
import { useState, useEffect } from 'react';
import { Heart, MessageCircle, Share2, Users, Trophy, Star, Calendar, Search, Filter } from 'lucide-react';
import { useAuthStore } from '../stores/authStore';
import { useGameStore } from '../stores/gameStore';
import Button from '../components/Button';
import Loading from '../components/Loading';

interface Post {
  id: string;
  user: {
    id: string;
    nickname: string;
    avatar: string;
    level: number;
  };
  content: string;
  image?: string;
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  createdAt: string;
  tags: string[];
}

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockedBy: number;
}

const Community = () => {
  const { isAuthenticated, user } = useAuthStore();
  const { gameRecords, isLoading } = useGameStore();
  
  const [activeTab, setActiveTab] = useState<'posts' | 'leaderboard' | 'achievements'>('posts');
  const [posts, setPosts] = useState<Post[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Mock data for demonstration
  useEffect(() => {
    // Mock posts
    setPosts([
      {
        id: '1',
        user: {
          id: '1',
          nickname: '摸鱼大师',
          avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20cartoon%20office%20worker%20avatar%20smiling&image_size=square',
          level: 15
        },
        content: '今天成功在老板眼皮底下刷了2小时视频，打工姜君真是我的守护神！ 🎮✨',
        likes: 42,
        comments: 8,
        shares: 3,
        isLiked: false,
        createdAt: '2024-01-15T10:30:00Z',
        tags: ['摸鱼技巧', '日常']
      },
      {
        id: '2',
        user: {
          id: '2',
          nickname: '办公室隐形人',
          avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20cartoon%20ninja%20office%20worker%20avatar&image_size=square',
          level: 12
        },
        content: '分享一个摸鱼小技巧：在Excel里玩贪吃蛇，老板永远发现不了！',
        image: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=excel%20spreadsheet%20with%20snake%20game%20hidden%20inside&image_size=landscape_4_3',
        likes: 67,
        comments: 15,
        shares: 12,
        isLiked: true,
        createdAt: '2024-01-14T15:45:00Z',
        tags: ['摸鱼技巧', '教程']
      },
      {
        id: '3',
        user: {
          id: '3',
          nickname: '咖啡续命师',
          avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20cartoon%20coffee%20lover%20office%20worker%20avatar&image_size=square',
          level: 8
        },
        content: '打工姜君陪我度过了又一个加班夜，有它在真的不孤单 ☕️💕',
        likes: 28,
        comments: 5,
        shares: 2,
        isLiked: false,
        createdAt: '2024-01-13T22:15:00Z',
        tags: ['加班', '陪伴']
      }
    ]);

    // Mock achievements
    setAchievements([
      {
        id: '1',
        name: '摸鱼新手',
        description: '完成第一次摸鱼游戏',
        icon: '🐟',
        rarity: 'common',
        unlockedBy: 1250
      },
      {
        id: '2',
        name: '隐身大师',
        description: '连续7天未被老板发现',
        icon: '👻',
        rarity: 'rare',
        unlockedBy: 340
      },
      {
        id: '3',
        name: '摸鱼传说',
        description: '单局游戏得分超过500分',
        icon: '🏆',
        rarity: 'epic',
        unlockedBy: 89
      },
      {
        id: '4',
        name: '时间管理大师',
        description: '在工作时间完成100次摸鱼',
        icon: '⏰',
        rarity: 'legendary',
        unlockedBy: 12
      }
    ]);

    // Mock leaderboard
    setLeaderboard([
      { rank: 1, nickname: '摸鱼之王', score: 2580, level: 25, avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=crown%20wearing%20office%20worker%20avatar&image_size=square' },
      { rank: 2, nickname: '隐形冠军', score: 2340, level: 23, avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=sneaky%20office%20worker%20avatar&image_size=square' },
      { rank: 3, nickname: '咖啡续命师', score: 2180, level: 21, avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=coffee%20loving%20office%20worker%20avatar&image_size=square' },
      { rank: 4, nickname: '午休专家', score: 1950, level: 19, avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=sleepy%20office%20worker%20avatar&image_size=square' },
      { rank: 5, nickname: '摸鱼大师', score: 1820, level: 18, avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=master%20office%20worker%20avatar&image_size=square' }
    ]);
  }, []);

  const handleLike = (postId: string) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { ...post, isLiked: !post.isLiked, likes: post.isLiked ? post.likes - 1 : post.likes + 1 }
        : post
    ));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return '刚刚';
    if (diffInHours < 24) return `${diffInHours}小时前`;
    if (diffInHours < 48) return '昨天';
    return date.toLocaleDateString('zh-CN');
  };

  const getRarityColor = (rarity: Achievement['rarity']) => {
    switch (rarity) {
      case 'common': return 'text-gray-600 bg-gray-100';
      case 'rare': return 'text-blue-600 bg-blue-100';
      case 'epic': return 'text-purple-600 bg-purple-100';
      case 'legendary': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.user.nickname.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || post.tags.includes(selectedFilter);
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            🌟 摸鱼社群
          </h1>
          <p className="text-xl text-gray-600">
            与千万打工人一起，分享摸鱼心得，交流生存技巧
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-3xl shadow-xl p-2 mb-8">
          <div className="flex space-x-2">
            {[
              { key: 'posts', label: '动态广场', icon: MessageCircle },
              { key: 'leaderboard', label: '排行榜', icon: Trophy },
              { key: 'achievements', label: '成就系统', icon: Star }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-2xl transition-all duration-200 ${
                  activeTab === key
                    ? 'bg-gradient-to-r from-ginger-500 to-vibrant-500 text-white shadow-lg'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-semibold">{label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Posts Tab */}
        {activeTab === 'posts' && (
          <div className="space-y-8">
            {/* Search and Filter */}
            <div className="bg-white rounded-3xl shadow-xl p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="搜索动态或用户..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ginger-500 focus:border-transparent"
                  />
                </div>
                <div className="relative">
                  <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <select
                    value={selectedFilter}
                    onChange={(e) => setSelectedFilter(e.target.value)}
                    className="pl-10 pr-8 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-400 focus:border-transparent appearance-none bg-white"
                  >
                    <option value="all">全部标签</option>
                    <option value="摸鱼技巧">摸鱼技巧</option>
                    <option value="日常">日常</option>
                    <option value="教程">教程</option>
                    <option value="加班">加班</option>
                    <option value="陪伴">陪伴</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Post Creation */}
            {isAuthenticated && (
              <div className="bg-white rounded-3xl shadow-xl p-6">
                <div className="flex items-start space-x-4">
                  <img
                    src={user?.avatar || 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=default%20user%20avatar&image_size=square'}
                    alt="Avatar"
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <textarea
                      placeholder="分享你的摸鱼心得..."
                      className="w-full p-4 border border-gray-200 rounded-xl resize-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                      rows={3}
                    />
                    <div className="flex justify-between items-center mt-4">
                      <div className="flex space-x-2">
                        {['摸鱼技巧', '日常', '教程'].map(tag => (
                          <button
                            key={tag}
                            className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-orange-100 hover:text-orange-600 transition-colors"
                          >
                            #{tag}
                          </button>
                        ))}
                      </div>
                      <Button size="sm">
                        发布动态
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Posts List */}
            <div className="space-y-6">
              {filteredPosts.map(post => (
                <div key={post.id} className="bg-white rounded-3xl shadow-xl p-6">
                  {/* Post Header */}
                  <div className="flex items-center space-x-4 mb-4">
                    <img
                      src={post.user.avatar}
                      alt={post.user.nickname}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900">{post.user.nickname}</h3>
                        <span className="px-2 py-1 text-xs bg-gradient-to-r from-orange-400 to-pink-400 text-white rounded-full">
                          Lv.{post.user.level}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {formatDate(post.createdAt)}
                      </p>
                    </div>
                  </div>

                  {/* Post Content */}
                  <div className="mb-4">
                    <p className="text-gray-700 mb-3">{post.content}</p>
                    {post.image && (
                      <img
                        src={post.image}
                        alt="Post image"
                        className="w-full max-w-md rounded-xl object-cover"
                      />
                    )}
                    <div className="flex flex-wrap gap-2 mt-3">
                      {post.tags.map(tag => (
                        <span
                          key={tag}
                          className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Post Actions */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div className="flex items-center space-x-6">
                      <button
                        onClick={() => handleLike(post.id)}
                        className={`flex items-center space-x-2 transition-colors ${
                          post.isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
                        }`}
                      >
                        <Heart className={`w-5 h-5 ${post.isLiked ? 'fill-current' : ''}`} />
                        <span>{post.likes}</span>
                      </button>
                      <button className="flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors">
                        <MessageCircle className="w-5 h-5" />
                        <span>{post.comments}</span>
                      </button>
                      <button className="flex items-center space-x-2 text-gray-500 hover:text-green-500 transition-colors">
                        <Share2 className="w-5 h-5" />
                        <span>{post.shares}</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Leaderboard Tab */}
        {activeTab === 'leaderboard' && (
          <div className="bg-white rounded-3xl shadow-xl p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">🏆 摸鱼排行榜</h2>
              <p className="text-gray-600">看看谁是真正的摸鱼之王！</p>
            </div>

            <div className="space-y-4">
              {leaderboard.map((player, index) => (
                <div
                  key={player.rank}
                  className={`flex items-center justify-between p-6 rounded-2xl ${
                    index < 3 ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200' : 'bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className="text-3xl">
                      {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `#${player.rank}`}
                    </div>
                    <img
                      src={player.avatar}
                      alt={player.nickname}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">{player.nickname}</h3>
                      <p className="text-sm text-gray-500">等级 {player.level}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-orange-600">{player.score}</div>
                    <div className="text-sm text-gray-500">总分</div>
                  </div>
                </div>
              ))}
            </div>

            {isAuthenticated && (
              <div className="mt-8 p-6 bg-blue-50 rounded-2xl">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <img
                      src={user?.avatar || 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=default%20user%20avatar&image_size=square'}
                      alt="My avatar"
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">{user?.nickname || '我'}</h3>
                      <p className="text-sm text-gray-500">我的排名</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-blue-600">#42</div>
                    <div className="text-sm text-gray-500">1,250分</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Achievements Tab */}
        {activeTab === 'achievements' && (
          <div className="bg-white rounded-3xl shadow-xl p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">⭐ 成就系统</h2>
              <p className="text-gray-600">解锁各种成就，展示你的摸鱼实力！</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {achievements.map(achievement => (
                <div
                  key={achievement.id}
                  className="p-6 border-2 border-gray-200 rounded-2xl hover:border-ginger-500 transition-colors"
                >
                  <div className="text-center mb-4">
                    <div className="text-4xl mb-2">{achievement.icon}</div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{achievement.name}</h3>
                    <p className="text-sm text-gray-600 mb-3">{achievement.description}</p>
                    <span className={`inline-block px-3 py-1 text-xs font-semibold rounded-full ${getRarityColor(achievement.rarity)}`}>
                      {achievement.rarity.toUpperCase()}
                    </span>
                  </div>
                  <div className="text-center pt-4 border-t border-gray-100">
                    <div className="flex items-center justify-center space-x-2 text-gray-500">
                      <Users className="w-4 h-4" />
                      <span className="text-sm">{achievement.unlockedBy.toLocaleString()} 人解锁</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {isAuthenticated && (
              <div className="mt-8 p-6 bg-green-50 rounded-2xl">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">我的成就进度</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">摸鱼新手</span>
                    <span className="text-green-600 font-semibold">✅ 已解锁</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">隐身大师</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-400 h-2 rounded-full" style={{ width: '60%' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">4/7 天</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">摸鱼传说</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-400 h-2 rounded-full" style={{ width: '30%' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">150/500 分</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Call to Action for Non-authenticated Users */}
        {!isAuthenticated && (
          <div className="bg-gradient-to-r from-orange-400 to-pink-400 rounded-3xl p-8 text-center text-white mt-8">
            <div className="text-4xl mb-4">🚀</div>
            <h3 className="text-2xl font-bold mb-4">加入摸鱼社群</h3>
            <p className="text-lg mb-6 opacity-90">
              注册登录，与千万打工人一起分享摸鱼心得，解锁更多成就！
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="bg-white text-orange-600 border-white hover:bg-gray-50">
                立即登录
              </Button>
              <Button className="bg-white text-orange-600 hover:bg-gray-50">
                免费注册
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Community;