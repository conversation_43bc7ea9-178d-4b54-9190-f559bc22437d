import { <PERSON><PERSON><PERSON>, Spark<PERSON>, Heart, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../components/Button';

const Home = () => {
  const features = [
    {
      icon: <Sparkles className="w-8 h-8 text-ginger-500" />,
      title: "AI智能陪伴",
      description: "打工姜君用AI技术为你提供个性化的工作陪伴，让每一天都充满惊喜"
    },
    {
      icon: <Heart className="w-8 h-8 text-vibrant-500" />,
      title: "情感支持",
      description: "工作压力大？打工姜君懂你的心情，提供温暖的情感支持和鼓励"
    },
    {
      icon: <Zap className="w-8 h-8 text-ginger-600" />,
      title: "趣味互动",
      description: "丰富的互动游戏和表情包，让工作间隙变得有趣而充实"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-orange-50 via-pink-50 to-yellow-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-center lg:text-left">
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                遇见你的
                <span className="bg-gradient-to-r from-ginger-500 to-vibrant-500 bg-clip-text text-transparent">
                  AI摸鱼搭子
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                工作太累？压力太大？让打工姜君陪你一起摸鱼！
                <br />
                用AI的温暖，点亮你的每一个工作日。
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link to="/game">
                  <Button size="lg" className="group bg-gradient-to-r from-ginger-500 to-vibrant-500 hover:from-ginger-600 hover:to-vibrant-600">
                    开始摸鱼
                    <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                <Link to="/story">
                  <Button variant="outline" size="lg">
                    了解打工姜君
                  </Button>
                </Link>
              </div>
            </div>

            {/* Right Content - Character */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <div className="w-80 h-80 bg-gradient-to-br from-orange-200 to-pink-200 rounded-full flex items-center justify-center">
                  <div className="text-8xl animate-bounce">
                    🥰
                  </div>
                </div>
                <div className="absolute -top-4 -right-4 bg-white rounded-full p-3 shadow-lg animate-pulse">
                  <Sparkles className="w-6 h-6 text-orange-500" />
                </div>
                <div className="absolute -bottom-4 -left-4 bg-white rounded-full p-3 shadow-lg animate-pulse delay-1000">
                  <Heart className="w-6 h-6 text-vibrant-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              为什么选择打工姜君？
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              不只是一个AI助手，更是你工作路上的贴心伙伴
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center group hover:transform hover:scale-105 transition-all duration-300">
                <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 group-hover:shadow-lg">
                  <div className="flex justify-center mb-6">
                    <div className="p-4 bg-gray-50 rounded-full group-hover:bg-gradient-to-br group-hover:from-orange-50 group-hover:to-pink-50 transition-colors">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-400 to-pink-400">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
            准备好开始你的摸鱼之旅了吗？
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            加入千万用户，让工作变得更有趣
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button variant="secondary" size="lg" className="bg-white text-orange-600 hover:bg-gray-50">
                立即注册
              </Button>
            </Link>
            <Link to="/features">
              <Button variant="ghost" size="lg" className="text-white border-white hover:bg-white hover:text-orange-600">
                了解更多
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;