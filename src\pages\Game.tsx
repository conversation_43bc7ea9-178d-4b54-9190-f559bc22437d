/**
 * Game page - "工位摸鱼生存战" interactive game
 */
import { useState, useEffect, useCallback } from 'react';
import { Play, Pause, RotateCcw, Trophy, Star, Clock } from 'lucide-react';
import { useAuthStore } from '../stores/authStore';
import { useGameStore } from '../stores/gameStore';
import Button from '../components/Button';
import Loading from '../components/Loading';

interface GameState {
  isPlaying: boolean;
  score: number;
  level: number;
  timeLeft: number;
  bossNearby: boolean;
  currentAction: string;
  gameOver: boolean;
}

const Game = () => {
  const { isAuthenticated, user } = useAuthStore();
  const { saveGameRecord, gameRecords, isLoading } = useGameStore();
  
  const [gameState, setGameState] = useState<GameState>({
    isPlaying: false,
    score: 0,
    level: 1,
    timeLeft: 60,
    bossNearby: false,
    currentAction: '正常工作中...',
    gameOver: false
  });

  const [gameInterval, setGameInterval] = useState<NodeJS.Timeout | null>(null);
  const [bossInterval, setBossInterval] = useState<NodeJS.Timeout | null>(null);

  const actions = [
    { name: '喝水', emoji: '💧', points: 5, risk: 0.1 },
    { name: '看手机', emoji: '📱', points: 10, risk: 0.3 },
    { name: '聊天', emoji: '💬', points: 15, risk: 0.4 },
    { name: '刷视频', emoji: '📺', points: 20, risk: 0.6 },
    { name: '打游戏', emoji: '🎮', points: 30, risk: 0.8 },
    { name: '睡觉', emoji: '😴', points: 50, risk: 0.9 }
  ];

  const expressions = [
    '😊', '😄', '🥰', '😎', '🤔', '😅', '🙃', '😋',
    '🤗', '😌', '😏', '🥳', '🤩', '😇', '🤭', '😉'
  ];

  const startGame = useCallback(() => {
    setGameState({
      isPlaying: true,
      score: 0,
      level: 1,
      timeLeft: 60,
      bossNearby: false,
      currentAction: '游戏开始！',
      gameOver: false
    });

    // Game timer
    const timer = setInterval(() => {
      setGameState(prev => {
        if (prev.timeLeft <= 1) {
          endGame(prev.score, prev.level);
          return { ...prev, timeLeft: 0, isPlaying: false, gameOver: true };
        }
        return { ...prev, timeLeft: prev.timeLeft - 1 };
      });
    }, 1000);

    // Boss appearance timer
    const bossTimer = setInterval(() => {
      const shouldAppear = Math.random() < 0.3;
      setGameState(prev => ({
        ...prev,
        bossNearby: shouldAppear,
        currentAction: shouldAppear ? '老板来了！快装作工作！' : '安全，继续摸鱼...'
      }));
    }, 3000);

    setGameInterval(timer);
    setBossInterval(bossTimer);
  }, []);

  const endGame = async (finalScore: number, finalLevel: number) => {
    if (gameInterval) clearInterval(gameInterval);
    if (bossInterval) clearInterval(bossInterval);
    
    if (isAuthenticated) {
      try {
        await saveGameRecord({
          gameType: 'survival',
          score: finalScore,
          level: finalLevel,
          duration: 60,
          achievements: finalScore > 500 ? ['high_score'] : [],
          gameData: {
            actionsPerformed: Math.floor(finalScore / 10),
            bossEncounters: Math.floor(Math.random() * 5) + 1
          }
        });
      } catch (error) {
        console.error('Failed to save game record:', error);
      }
    }
  };

  const performAction = (action: typeof actions[0]) => {
    if (!gameState.isPlaying || gameState.bossNearby) return;

    const caught = Math.random() < action.risk;
    if (caught) {
      setGameState(prev => ({
        ...prev,
        score: Math.max(0, prev.score - 20),
        currentAction: `被发现了！扣分 -20`
      }));
    } else {
      setGameState(prev => {
        const newScore = prev.score + action.points;
        const newLevel = Math.floor(newScore / 100) + 1;
        return {
          ...prev,
          score: newScore,
          level: newLevel,
          currentAction: `${action.name} +${action.points}分`
        };
      });
    }
  };

  const resetGame = () => {
    if (gameInterval) clearInterval(gameInterval);
    if (bossInterval) clearInterval(bossInterval);
    setGameState({
      isPlaying: false,
      score: 0,
      level: 1,
      timeLeft: 60,
      bossNearby: false,
      currentAction: '准备开始游戏...',
      gameOver: false
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    return () => {
      if (gameInterval) clearInterval(gameInterval);
      if (bossInterval) clearInterval(bossInterval);
    };
  }, [gameInterval, bossInterval]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            🎮 工位摸鱼生存战
          </h1>
          <p className="text-xl text-gray-600">
            在老板的眼皮底下，看你能摸多少鱼！
          </p>
        </div>

        {/* Game Area */}
        <div className="bg-white rounded-3xl shadow-xl p-8 mb-8">
          {/* Game Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-gradient-to-r from-orange-400 to-pink-400 rounded-2xl p-4 text-white text-center">
              <div className="text-2xl font-bold">{gameState.score}</div>
              <div className="text-sm opacity-90">分数</div>
            </div>
            <div className="bg-gradient-to-r from-blue-400 to-purple-400 rounded-2xl p-4 text-white text-center">
              <div className="text-2xl font-bold">{gameState.level}</div>
              <div className="text-sm opacity-90">等级</div>
            </div>
            <div className="bg-gradient-to-r from-green-400 to-blue-400 rounded-2xl p-4 text-white text-center">
              <div className="text-2xl font-bold">{formatTime(gameState.timeLeft)}</div>
              <div className="text-sm opacity-90">剩余时间</div>
            </div>
            <div className="bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl p-4 text-white text-center">
              <div className="text-2xl">
                {gameState.bossNearby ? '😱' : expressions[gameState.level % expressions.length]}
              </div>
              <div className="text-sm opacity-90">状态</div>
            </div>
          </div>

          {/* Game Status */}
          <div className="text-center mb-8">
            <div className={`text-2xl font-bold p-4 rounded-2xl ${
              gameState.bossNearby 
                ? 'bg-red-100 text-red-600' 
                : gameState.isPlaying 
                ? 'bg-green-100 text-green-600'
                : 'bg-gray-100 text-gray-600'
            }`}>
              {gameState.currentAction}
            </div>
          </div>

          {/* Game Controls */}
          <div className="text-center mb-8">
            {!gameState.isPlaying && !gameState.gameOver && (
              <Button size="lg" onClick={startGame} className="mr-4">
                <Play className="w-5 h-5 mr-2" />
                开始游戏
              </Button>
            )}
            {gameState.isPlaying && (
              <Button size="lg" variant="outline" onClick={resetGame} className="mr-4">
                <Pause className="w-5 h-5 mr-2" />
                暂停游戏
              </Button>
            )}
            <Button size="lg" variant="outline" onClick={resetGame}>
              <RotateCcw className="w-5 h-5 mr-2" />
              重新开始
            </Button>
          </div>

          {/* Action Buttons */}
          {gameState.isPlaying && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => performAction(action)}
                  disabled={gameState.bossNearby}
                  className={`p-4 rounded-2xl border-2 transition-all duration-200 ${
                    gameState.bossNearby
                      ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white border-gray-200 hover:border-orange-400 hover:bg-orange-50 text-gray-700 hover:text-orange-600'
                  }`}
                >
                  <div className="text-3xl mb-2">{action.emoji}</div>
                  <div className="font-semibold">{action.name}</div>
                  <div className="text-sm text-gray-500">+{action.points}分</div>
                  <div className="text-xs text-red-500">风险: {Math.round(action.risk * 100)}%</div>
                </button>
              ))}
            </div>
          )}

          {/* Game Over */}
          {gameState.gameOver && (
            <div className="text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">游戏结束！</h2>
              <p className="text-xl text-gray-600 mb-6">
                最终得分: <span className="font-bold text-orange-600">{gameState.score}</span> 分
              </p>
              {gameState.score > 300 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-4 mb-6">
                  <div className="flex items-center justify-center space-x-2 text-yellow-600">
                    <Trophy className="w-6 h-6" />
                    <span className="font-semibold">恭喜！解锁新表情包！</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Game Rules */}
        <div className="bg-white rounded-3xl shadow-xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            🎯 游戏规则
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">如何得分</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 选择不同的摸鱼行为获得分数</li>
                <li>• 风险越高的行为得分越多</li>
                <li>• 达到100分升级，解锁新表情</li>
                <li>• 游戏时长60秒，争取最高分</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">注意事项</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 老板出现时不能进行任何操作</li>
                <li>• 被发现摸鱼会扣除20分</li>
                <li>• 高风险行为更容易被发现</li>
                <li>• 保持警惕，见机行事！</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Leaderboard */}
        {isAuthenticated && (
          <div className="bg-white rounded-3xl shadow-xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              🏆 我的游戏记录
            </h3>
            {isLoading ? (
              <Loading text="加载中..." className="py-8" />
            ) : gameRecords.length > 0 ? (
              <div className="space-y-4">
                {gameRecords.slice(0, 5).map((record, index) => (
                  <div key={record._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">
                        {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'}
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">{record.score} 分</div>
                        <div className="text-sm text-gray-500">
                          等级 {record.level} • {new Date(record.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-500">{record.duration}s</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                还没有游戏记录，快来开始第一局吧！
              </div>
            )}
          </div>
        )}

        {!isAuthenticated && (
          <div className="bg-orange-50 border border-orange-200 rounded-3xl p-8 text-center">
            <div className="text-4xl mb-4">🔐</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              登录后保存游戏记录
            </h3>
            <p className="text-gray-600 mb-6">
              注册登录后可以保存你的游戏成绩，解锁更多表情包！
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline">
                登录
              </Button>
              <Button>
                注册
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Game;