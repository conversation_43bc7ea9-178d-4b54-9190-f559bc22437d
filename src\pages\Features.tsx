/**
 * Features page showcasing product capabilities
 */
import { Brain, MessageCircle, Gamepad2, Trophy, Users, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../components/Button';

const Features = () => {
  const mainFeatures = [
    {
      icon: <Brain className="w-12 h-12 text-orange-500" />,
      title: "AI智能对话",
      description: "基于先进的AI技术，打工姜君能够理解你的情绪和需求，提供个性化的对话体验。无论是工作压力还是生活烦恼，打工姜君都能给出贴心的回应。",
      features: ["情感识别", "个性化回复", "上下文理解", "多轮对话"]
    },
    {
      icon: <MessageCircle className="w-12 h-12 text-pink-500" />,
      title: "情感陪伴",
      description: "不只是冰冷的AI，打工姜君有着温暖的性格。她会记住你的喜好，关心你的状态，在你需要的时候给予鼓励和支持。",
      features: ["情绪安慰", "积极鼓励", "个性记忆", "贴心提醒"]
    },
    {
      icon: <Gamepad2 className="w-12 h-12 text-blue-500" />,
      title: "互动游戏",
      description: "工作间隙来一局《工位摸鱼生存战》！通过有趣的小游戏释放压力，同时还能解锁更多打工姜君的可爱表情。",
      features: ["摸鱼小游戏", "表情解锁", "成就系统", "排行榜"]
    },
    {
      icon: <Trophy className="w-12 h-12 text-yellow-500" />,
      title: "成长系统",
      description: "与打工姜君的每一次互动都会让你们的关系更进一步。解锁新表情、获得成就徽章，见证你们友谊的成长历程。",
      features: ["等级系统", "成就徽章", "表情收集", "互动记录"]
    },
    {
      icon: <Users className="w-12 h-12 text-green-500" />,
      title: "社区互动",
      description: "加入摸鱼搭子大家庭，与其他用户分享你的摸鱼心得，交流工作趣事，一起在职场路上相互支持。",
      features: ["用户社区", "经验分享", "话题讨论", "好友系统"]
    },
    {
      icon: <Zap className="w-12 h-12 text-purple-500" />,
      title: "智能提醒",
      description: "打工姜君会根据你的工作习惯，在合适的时间提醒你休息、喝水或者来一局小游戏，帮你保持最佳的工作状态。",
      features: ["休息提醒", "健康建议", "工作节奏", "个性化推荐"]
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-50 via-pink-50 to-yellow-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            强大功能，
            <span className="bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text text-transparent">
              贴心体验
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            打工姜君不只是一个AI助手，更是你工作路上的贴心伙伴。
            让我们一起探索那些让工作变得有趣的功能吧！
          </p>
          <Link to="/game">
            <Button size="lg">
              立即体验
            </Button>
          </Link>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {mainFeatures.map((feature, index) => (
              <div key={index} className="group">
                <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 h-full">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-gradient-to-br group-hover:from-orange-50 group-hover:to-pink-50 transition-colors">
                        {feature.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {feature.description}
                      </p>
                      <div className="grid grid-cols-2 gap-2">
                        {feature.features.map((item, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-gradient-to-r from-ginger-500 to-vibrant-500 rounded-full"></div>
                            <span className="text-sm text-gray-700">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How it Works */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              如何开始使用？
            </h2>
            <p className="text-xl text-gray-600">
              三步即可开启你的摸鱼之旅
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-white">1</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">注册账号</h3>
              <p className="text-gray-600">
                快速注册，创建你的专属档案，让打工姜君更好地了解你
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-white">2</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">开始对话</h3>
              <p className="text-gray-600">
                与打工姜君聊天，分享你的工作状态和心情，建立专属的互动关系
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-white">3</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">享受摸鱼</h3>
              <p className="text-gray-600">
                玩游戏、收集表情、获得成就，让工作变得更加有趣
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-400 to-pink-400">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
            准备好体验这些功能了吗？
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            立即注册，开启你的AI摸鱼搭子之旅
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button variant="secondary" size="lg" className="bg-white text-orange-600 hover:bg-gray-50">
                立即注册
              </Button>
            </Link>
            <Link to="/story">
              <Button variant="ghost" size="lg" className="text-white border-white hover:bg-white hover:text-orange-600">
                了解打工姜君
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Features;