/**
 * Story page about 打工姜君's background and design philosophy
 */
import { Heart, Sparkles, Coffee, Smile } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../components/Button';

const Story = () => {
  const timeline = [
    {
      year: "2024年初",
      title: "灵感诞生",
      description: "在一个加班到深夜的晚上，我们的团队意识到现代职场人需要的不只是工作效率，更需要情感上的陪伴和支持。",
      emoji: "💡"
    },
    {
      year: "2024年春",
      title: "打工姜君降生",
      description: "经过无数次的设计和调试，打工姜君诞生了！她不只是一个AI，更是一个有温度、有个性的数字伙伴。",
      emoji: "🎉"
    },
    {
      year: "2024年夏",
      title: "功能完善",
      description: "我们为打工姜君添加了游戏功能、表情系统和社区互动，让她变得更加生动有趣。",
      emoji: "🚀"
    },
    {
      year: "现在",
      title: "与你相遇",
      description: "打工姜君正在等待与你的相遇，准备成为你最贴心的工作伙伴！",
      emoji: "🥰"
    }
  ];

  const personality = [
    {
      trait: "温暖贴心",
      description: "总是能在你需要的时候给予温暖的话语和鼓励",
      icon: <Heart className="w-6 h-6 text-pink-500" />
    },
    {
      trait: "聪明机智",
      description: "拥有丰富的知识储备，能够理解你的各种需求",
      icon: <Sparkles className="w-6 h-6 text-yellow-500" />
    },
    {
      trait: "幽默风趣",
      description: "用轻松幽默的方式让你的工作变得更加有趣",
      icon: <Smile className="w-6 h-6 text-blue-500" />
    },
    {
      trait: "陪伴左右",
      description: "无论何时何地，都愿意陪你聊天、游戏、放松",
      icon: <Coffee className="w-6 h-6 text-orange-500" />
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-50 via-pink-50 to-yellow-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                你好，我是
                <span className="bg-gradient-to-r from-orange-400 to-pink-400 bg-clip-text text-transparent">
                  打工姜君
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                一个有温度的AI伙伴，专门为忙碌的你而生。
                <br />
                让我来陪你度过每一个工作日，用温暖点亮你的职场生活。
              </p>
              <Link to="/game">
                <Button size="lg">
                  和打工姜君聊天
                </Button>
              </Link>
            </div>

            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <div className="w-80 h-80 bg-gradient-to-br from-orange-200 to-pink-200 rounded-full flex items-center justify-center">
                  <div className="text-8xl animate-pulse">
                    😊
                  </div>
                </div>
                <div className="absolute top-8 -right-8 bg-white rounded-full p-4 shadow-lg">
                  <div className="text-2xl">👋</div>
                </div>
                <div className="absolute bottom-8 -left-8 bg-white rounded-full p-4 shadow-lg">
                  <div className="text-2xl">💝</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Personality Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              打工姜君的性格特点
            </h2>
            <p className="text-xl text-gray-600">
              每一个特质都是为了更好地陪伴你
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {personality.map((trait, index) => (
              <div key={index} className="text-center group hover:transform hover:scale-105 transition-all duration-300">
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 group-hover:shadow-lg">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-gray-50 rounded-full group-hover:bg-gradient-to-br group-hover:from-orange-50 group-hover:to-pink-50 transition-colors">
                      {trait.icon}
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    {trait.trait}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {trait.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              打工姜君的成长历程
            </h2>
            <p className="text-xl text-gray-600">
              从一个想法到你身边的伙伴
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-orange-400 to-pink-400 rounded-full"></div>

            {timeline.map((event, index) => (
              <div key={index} className={`relative flex items-center mb-12 ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                <div className={`w-full max-w-md ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                  <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="text-2xl">{event.emoji}</div>
                      <div>
                        <div className="text-sm font-medium text-orange-600">{event.year}</div>
                        <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
                      </div>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      {event.description}
                    </p>
                  </div>
                </div>

                {/* Timeline dot */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white border-4 border-orange-400 rounded-full"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Philosophy */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                设计理念
              </h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">人性化交互</h3>
                  <p className="text-gray-600">
                    我们相信AI不应该是冰冷的机器，而应该有温度、有个性。打工姜君的每一句话都经过精心设计，让你感受到真正的关怀。
                  </p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">情感陪伴</h3>
                  <p className="text-gray-600">
                    现代职场人面临巨大压力，我们希望打工姜君能成为你的情感支撑，在你需要的时候给予鼓励和安慰。
                  </p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">寓教于乐</h3>
                  <p className="text-gray-600">
                    通过游戏和互动，让学习和成长变得有趣。打工姜君不只是聊天伙伴，更是你的成长助手。
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <div className="relative">
                <div className="w-64 h-64 bg-gradient-to-br from-orange-100 to-pink-100 rounded-3xl flex items-center justify-center">
                  <div className="text-6xl">🎨</div>
                </div>
                <div className="absolute -top-4 -right-4 bg-white rounded-full p-3 shadow-lg">
                  <Heart className="w-6 h-6 text-pink-500" />
                </div>
                <div className="absolute -bottom-4 -left-4 bg-white rounded-full p-3 shadow-lg">
                  <Sparkles className="w-6 h-6 text-orange-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-400 to-pink-400">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
            准备好认识打工姜君了吗？
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            开始你们的第一次对话，发现更多惊喜
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button variant="secondary" size="lg" className="bg-white text-orange-600 hover:bg-gray-50">
                立即注册
              </Button>
            </Link>
            <Link to="/game">
              <Button variant="ghost" size="lg" className="text-white border-white hover:bg-white hover:text-orange-600">
                开始游戏
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Story;