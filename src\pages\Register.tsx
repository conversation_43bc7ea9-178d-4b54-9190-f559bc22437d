/**
 * Register page - User registration
 */
import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, User, ArrowRight, Check } from 'lucide-react';
import { useAuthStore } from '../stores/authStore';
import Button from '../components/Button';
import Input from '../components/Input';
import Loading from '../components/Loading';

interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  nickname: string;
}

const Register = () => {
  const navigate = useNavigate();
  const { register, isAuthenticated, isLoading, error, clearError } = useAuthStore();
  
  const [form, setForm] = useState<RegisterForm>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    nickname: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<Partial<RegisterForm>>({});
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  const validateForm = (): boolean => {
    const errors: Partial<RegisterForm> = {};

    // Username validation
    if (!form.username) {
      errors.username = '请输入用户名';
    } else if (form.username.length < 3) {
      errors.username = '用户名至少需要3个字符';
    } else if (form.username.length > 20) {
      errors.username = '用户名不能超过20个字符';
    } else if (!/^[a-zA-Z0-9_]+$/.test(form.username)) {
      errors.username = '用户名只能包含字母、数字和下划线';
    }

    // Email validation
    if (!form.email) {
      errors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    // Password validation
    if (!form.password) {
      errors.password = '请输入密码';
    } else if (form.password.length < 6) {
      errors.password = '密码至少需要6个字符';
    } else if (form.password.length > 50) {
      errors.password = '密码不能超过50个字符';
    }

    // Confirm password validation
    if (!form.confirmPassword) {
      errors.confirmPassword = '请确认密码';
    } else if (form.password !== form.confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
    }

    // Nickname validation
    if (!form.nickname) {
      errors.nickname = '请输入昵称';
    } else if (form.nickname.length < 2) {
      errors.nickname = '昵称至少需要2个字符';
    } else if (form.nickname.length > 20) {
      errors.nickname = '昵称不能超过20个字符';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!agreedToTerms) {
      alert('请同意用户协议和隐私政策');
      return;
    }

    try {
      await register(form.username, form.email, form.password, form.nickname);
      // Navigation will be handled by the useEffect above
    } catch (err) {
      // Error is handled by the store
      console.error('Registration failed:', err);
    }
  };

  const handleInputChange = (field: keyof RegisterForm) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm(prev => ({ ...prev, [field]: e.target.value }));
    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
    // Clear global error
    if (error) {
      clearError();
    }
  };

  const getPasswordStrength = (password: string): { strength: number; label: string; color: string } => {
    if (password.length === 0) return { strength: 0, label: '', color: '' };
    
    let score = 0;
    if (password.length >= 6) score += 1;
    if (password.length >= 8) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^a-zA-Z0-9]/.test(password)) score += 1;

    if (score <= 2) return { strength: 33, label: '弱', color: 'bg-red-400' };
    if (score <= 4) return { strength: 66, label: '中', color: 'bg-yellow-400' };
    return { strength: 100, label: '强', color: 'bg-green-400' };
  };

  const passwordStrength = getPasswordStrength(form.password);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="text-6xl mb-4">🚀</div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            加入摸鱼大家庭
          </h2>
          <p className="text-gray-600">
            创建你的账户，开始你的摸鱼之旅
          </p>
        </div>

        {/* Registration Form */}
        <div className="bg-white rounded-3xl shadow-xl p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Global Error */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-center space-x-2 text-red-600">
                  <div className="text-sm font-medium">{error}</div>
                </div>
              </div>
            )}

            {/* Username Field */}
            <div>
              <Input
                label="用户名"
                type="text"
                value={form.username}
                onChange={handleInputChange('username')}
                error={formErrors.username}
                placeholder="请输入用户名（3-20个字符）"
                icon={User}
                fullWidth
                helperText="用户名只能包含字母、数字和下划线"
              />
            </div>

            {/* Nickname Field */}
            <div>
              <Input
                label="昵称"
                type="text"
                value={form.nickname}
                onChange={handleInputChange('nickname')}
                error={formErrors.nickname}
                placeholder="请输入昵称（2-20个字符）"
                icon={User}
                fullWidth
                helperText="昵称将在社区中显示"
              />
            </div>

            {/* Email Field */}
            <div>
              <Input
                label="邮箱地址"
                type="email"
                value={form.email}
                onChange={handleInputChange('email')}
                error={formErrors.email}
                placeholder="请输入你的邮箱地址"
                icon={Mail}
                fullWidth
                helperText="用于账户验证和找回密码"
              />
            </div>

            {/* Password Field */}
            <div>
              <div className="relative">
                <Input
                  label="密码"
                  type={showPassword ? 'text' : 'password'}
                  value={form.password}
                  onChange={handleInputChange('password')}
                  error={formErrors.password}
                  placeholder="请输入密码（至少6个字符）"
                  icon={Lock}
                  fullWidth
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              
              {/* Password Strength Indicator */}
              {form.password && (
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                        style={{ width: `${passwordStrength.strength}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600">
                      密码强度: {passwordStrength.label}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Confirm Password Field */}
            <div>
              <div className="relative">
                <Input
                  label="确认密码"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={form.confirmPassword}
                  onChange={handleInputChange('confirmPassword')}
                  error={formErrors.confirmPassword}
                  placeholder="请再次输入密码"
                  icon={Lock}
                  fullWidth
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start space-x-3">
              <div className="flex items-center h-5">
                <input
                  type="checkbox"
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                />
              </div>
              <div className="text-sm text-gray-600">
                我已阅读并同意{' '}
                <Link to="/terms" className="text-orange-600 hover:text-orange-500">
                  用户协议
                </Link>
                {' '}和{' '}
                <Link to="/privacy" className="text-orange-600 hover:text-orange-500">
                  隐私政策
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              size="lg"
              loading={isLoading}
              disabled={isLoading || !agreedToTerms}
            >
              {isLoading ? (
                <Loading size="sm" />
              ) : (
                <>
                  创建账户
                  <ArrowRight className="w-5 h-5 ml-2" />
                </>
              )}
            </Button>
          </form>

          {/* Divider */}
          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">或者</span>
              </div>
            </div>
          </div>

          {/* Social Registration */}
          <div className="mt-6 space-y-3">
            <Button
              variant="outline"
              fullWidth
              onClick={() => alert('微信注册功能开发中...')}
            >
              <div className="text-green-500 text-xl mr-2">💬</div>
              使用微信注册
            </Button>
            <Button
              variant="outline"
              fullWidth
              onClick={() => alert('QQ注册功能开发中...')}
            >
              <div className="text-blue-500 text-xl mr-2">🐧</div>
              使用QQ注册
            </Button>
          </div>

          {/* Login Link */}
          <div className="mt-8 text-center">
            <p className="text-gray-600">
              已经有账户了？{' '}
              <Link
                to="/login"
                className="text-orange-600 hover:text-orange-500 font-semibold transition-colors"
              >
                立即登录
              </Link>
            </p>
          </div>
        </div>

        {/* Registration Benefits */}
        <div className="bg-white rounded-3xl shadow-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
            注册即可享受
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Check className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-sm text-gray-600">
                保存游戏记录和成就进度
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Check className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-sm text-gray-600">
                参与社区互动，分享摸鱼心得
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Check className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-sm text-gray-600">
                解锁专属表情包和个性化设置
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Check className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-sm text-gray-600">
                获得排行榜排名和竞技功能
              </div>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center">
          <Link
            to="/"
            className="text-gray-500 hover:text-gray-700 transition-colors inline-flex items-center"
          >
            ← 返回首页
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Register;