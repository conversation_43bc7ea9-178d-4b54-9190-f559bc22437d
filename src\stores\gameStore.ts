/**
 * Game store using Zustand
 */
import { create } from 'zustand';
import { useAuthStore } from './authStore';

export interface GameRecord {
  id: string;
  _id?: string;
  gameType: 'survival' | 'expression' | 'quiz';
  score: number;
  duration: number;
  level: number;
  achievements: string[];
  createdAt: string;
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  nickname?: string;
  avatar?: string;
  highestScore: number;
  totalGames: number;
  lastPlayed: string;
}

interface GameState {
  currentGame: {
    gameType: 'survival' | 'expression' | 'quiz' | null;
    score: number;
    level: number;
    isPlaying: boolean;
    startTime: number | null;
    duration: number;
  };
  gameRecords: GameRecord[];
  leaderboard: LeaderboardEntry[];
  isLoading: boolean;
  error: string | null;
}

interface GameActions {
  startGame: (gameType: 'survival' | 'expression' | 'quiz') => void;
  endGame: () => Promise<void>;
  updateScore: (score: number) => void;
  updateLevel: (level: number) => void;
  saveGameRecord: (gameData: any) => Promise<void>;
  fetchGameRecords: () => Promise<void>;
  getGameRecords: () => Promise<void>;
  fetchLeaderboard: (gameType?: string) => Promise<void>;
  clearError: () => void;
}

type GameStore = GameState & GameActions;

const API_BASE_URL = 'http://localhost:3001/api';

export const useGameStore = create<GameStore>((set, get) => ({
  // State
  currentGame: {
    gameType: null,
    score: 0,
    level: 1,
    isPlaying: false,
    startTime: null,
    duration: 0,
  },
  gameRecords: [],
  leaderboard: [],
  isLoading: false,
  error: null,

  // Actions
  startGame: (gameType) => {
    set({
      currentGame: {
        gameType,
        score: 0,
        level: 1,
        isPlaying: true,
        startTime: Date.now(),
        duration: 0,
      },
      error: null,
    });
  },

  endGame: async () => {
    const { currentGame } = get();
    if (!currentGame.isPlaying || !currentGame.startTime) {
      return;
    }

    const duration = Math.floor((Date.now() - currentGame.startTime) / 1000);
    
    set({
      currentGame: {
        ...currentGame,
        isPlaying: false,
        duration,
      },
    });

    // Auto-save game record
    await get().saveGameRecord({
      gameType: currentGame.gameType,
      score: currentGame.score,
      duration,
      level: currentGame.level,
    });
  },

  updateScore: (score) => {
    const { currentGame } = get();
    set({
      currentGame: {
        ...currentGame,
        score,
      },
    });
  },

  updateLevel: (level) => {
    const { currentGame } = get();
    set({
      currentGame: {
        ...currentGame,
        level,
      },
    });
  },

  saveGameRecord: async (gameData) => {
    const { token } = useAuthStore.getState();
    
    if (!token) {
      set({ error: 'User not authenticated' });
      return;
    }

    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`${API_BASE_URL}/game/record`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(gameData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save game record');
      }

      // Refresh game records
      await get().fetchGameRecords();
      
      set({ isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to save game record',
        isLoading: false,
      });
    }
  },

  fetchGameRecords: async () => {
    const { token } = useAuthStore.getState();
    
    if (!token) {
      return;
    }

    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`${API_BASE_URL}/game/records`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch game records');
      }

      set({
        gameRecords: data.data.records,
        isLoading: false,
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch game records',
        isLoading: false,
      });
    }
  },

  getGameRecords: async () => {
    await get().fetchGameRecords();
  },

  fetchLeaderboard: async (gameType = 'survival') => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`${API_BASE_URL}/game/leaderboard?gameType=${gameType}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch leaderboard');
      }

      set({
        leaderboard: data.data.leaderboard,
        isLoading: false,
      });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch leaderboard',
        isLoading: false,
      });
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));