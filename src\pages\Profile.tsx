/**
 * Profile page - User profile and settings
 */
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  User,
  Settings,
  Trophy,
  Target,
  Clock,
  Star,
  Edit3,
  Camera,
  Save,
  X,
  Medal,
  TrendingUp,
  Calendar,
  Award,
  Crown,
  Zap
} from 'lucide-react';
import { useAuthStore } from '../stores/authStore';
import { useGameStore } from '../stores/gameStore';
import Button from '../components/Button';
import Input from '../components/Input';
import Loading from '../components/Loading';

interface EditForm {
  nickname: string;
  email: string;
  favoriteEmoji: string;
  theme: 'light' | 'dark' | 'auto';
}

const Profile = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, updateUser, isLoading } = useAuthStore();
  const { gameRecords, getGameRecords, isLoading: gameLoading } = useGameStore();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'stats' | 'achievements' | 'settings'>('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<EditForm>({
    nickname: '',
    email: '',
    favoriteEmoji: '',
    theme: 'light'
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    // Load game records
    getGameRecords();
  }, [isAuthenticated, navigate, getGameRecords]);

  // Initialize edit form when user data is available
  useEffect(() => {
    if (user) {
      setEditForm({
        nickname: user.nickname || '',
        email: user.email || '',
        favoriteEmoji: user.preferences?.favoriteEmoji || '😊',
        theme: user.preferences?.theme || 'light'
      });
    }
  }, [user]);

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await updateUser({
        nickname: editForm.nickname,
        email: editForm.email,
        preferences: {
          favoriteExpressions: user?.preferences?.favoriteExpressions || [],
          favoriteEmoji: editForm.favoriteEmoji,
          theme: editForm.theme
        }
      });
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handleInputChange = (field: keyof EditForm) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setEditForm(prev => ({ ...prev, [field]: e.target.value }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  // Mock achievements data
  const achievements = [
    {
      id: 1,
      name: '摸鱼新手',
      description: '完成第一次游戏',
      icon: '🎯',
      unlocked: true,
      unlockedAt: '2024-01-15'
    },
    {
      id: 2,
      name: '摸鱼达人',
      description: '累计游戏时长超过1小时',
      icon: '⏰',
      unlocked: true,
      unlockedAt: '2024-01-20'
    },
    {
      id: 3,
      name: '分数王者',
      description: '单局得分超过1000分',
      icon: '👑',
      unlocked: user?.gameData?.highestScore && user.gameData.highestScore > 1000,
      unlockedAt: user?.gameData?.highestScore && user.gameData.highestScore > 1000 ? '2024-01-25' : undefined
    },
    {
      id: 4,
      name: '连击大师',
      description: '连续游戏7天',
      icon: '🔥',
      unlocked: false,
      unlockedAt: undefined
    },
    {
      id: 5,
      name: '社交达人',
      description: '在社区发布10条动态',
      icon: '💬',
      unlocked: false,
      unlockedAt: undefined
    },
    {
      id: 6,
      name: '摸鱼传说',
      description: '累计得分超过10000分',
      icon: '🌟',
      unlocked: false,
      unlockedAt: undefined
    }
  ];

  const unlockedAchievements = achievements.filter(a => a.unlocked);
  const totalAchievements = achievements.length;

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <Loading text="加载用户信息中..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <div className="bg-white rounded-3xl shadow-xl p-8 mb-8">
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-pink-400 rounded-full flex items-center justify-center text-white text-4xl font-bold">
                {user.avatar ? (
                  <img src={user.avatar} alt={user.nickname} className="w-full h-full rounded-full object-cover" />
                ) : (
                  user.nickname?.charAt(0).toUpperCase() || user.username?.charAt(0).toUpperCase()
                )}
              </div>
              <button className="absolute bottom-2 right-2 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                <Camera className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* User Info */}
            <div className="flex-1 text-center md:text-left">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {user.nickname || user.username}
                  </h1>
                  <p className="text-gray-600 mb-2">@{user.username}</p>
                  <p className="text-sm text-gray-500">
                    加入时间: {formatDate(user.createdAt || new Date().toISOString())}
                  </p>
                </div>
                <Button
                  variant={isEditing ? 'outline' : 'primary'}
                  onClick={() => setIsEditing(!isEditing)}
                  className="mt-4 md:mt-0"
                >
                  {isEditing ? (
                    <>
                      <X className="w-4 h-4 mr-2" />
                      取消编辑
                    </>
                  ) : (
                    <>
                      <Edit3 className="w-4 h-4 mr-2" />
                      编辑资料
                    </>
                  )}
                </Button>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {user.gameData?.totalScore || 0}
                  </div>
                  <div className="text-sm text-blue-500">总分</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {user.gameData?.highestScore || 0}
                  </div>
                  <div className="text-sm text-green-500">最高分</div>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {user.gameData?.gamesPlayed || 0}
                  </div>
                  <div className="text-sm text-purple-500">游戏次数</div>
                </div>
                <div className="bg-gradient-to-br from-ginger-50 to-ginger-100 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-ginger-600">
                    {unlockedAchievements.length}
                  </div>
                  <div className="text-sm text-ginger-500">成就</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-3xl shadow-xl mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-8">
              {[
                { id: 'overview', label: '概览', icon: User },
                { id: 'stats', label: '游戏统计', icon: TrendingUp },
                { id: 'achievements', label: '成就', icon: Trophy },
                { id: 'settings', label: '设置', icon: Settings }
              ].map(tab => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-orange-500 text-orange-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Icon className="w-4 h-4" />
                      <span>{tab.label}</span>
                    </div>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-8">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Recent Activity */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Calendar className="w-5 h-5 mr-2 text-orange-500" />
                    最近活动
                  </h3>
                  <div className="space-y-4">
                    {gameRecords.slice(0, 5).map((record, index) => (
                      <div key={record._id || index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full flex items-center justify-center">
                            <Target className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">
                              工位摸鱼生存战
                            </div>
                            <div className="text-sm text-gray-500">
                              得分: {record.score} | 等级: {record.level}
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(record.createdAt || new Date().toISOString())}
                        </div>
                      </div>
                    ))}
                    {gameRecords.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        还没有游戏记录，快去开始你的第一局游戏吧！
                      </div>
                    )}
                  </div>
                </div>

                {/* Favorite Emojis */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Star className="w-5 h-5 mr-2 text-orange-500" />
                    收藏表情
                  </h3>
                  <div className="grid grid-cols-6 md:grid-cols-12 gap-4">
                    {(user.gameData?.unlockedEmojis || ['😊', '😎', '🤔', '😴', '🎯', '🚀']).map((emoji, index) => (
                      <div
                        key={index}
                        className="w-12 h-12 bg-white border-2 border-gray-200 rounded-xl flex items-center justify-center text-2xl hover:border-orange-300 transition-colors cursor-pointer"
                      >
                        {emoji}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Stats Tab */}
            {activeTab === 'stats' && (
              <div className="space-y-8">
                {gameLoading ? (
                  <div className="text-center py-8">
                    <Loading text="加载游戏统计中..." />
                  </div>
                ) : (
                  <>
                    {/* Game Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold text-blue-900">总游戏时长</h4>
                          <Clock className="w-5 h-5 text-blue-600" />
                        </div>
                        <div className="text-3xl font-bold text-blue-600">
                          {formatDuration(gameRecords.reduce((total, record) => total + (record.duration || 0), 0))}
                        </div>
                      </div>
                      
                      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold text-green-900">平均分数</h4>
                          <TrendingUp className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="text-3xl font-bold text-green-600">
                          {gameRecords.length > 0 
                            ? Math.round(gameRecords.reduce((total, record) => total + record.score, 0) / gameRecords.length)
                            : 0
                          }
                        </div>
                      </div>
                      
                      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold text-purple-900">最高等级</h4>
                          <Medal className="w-5 h-5 text-purple-600" />
                        </div>
                        <div className="text-3xl font-bold text-purple-600">
                          {gameRecords.length > 0 
                            ? Math.max(...gameRecords.map(record => record.level || 1))
                            : 0
                          }
                        </div>
                      </div>
                    </div>

                    {/* Recent Games */}
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-6">游戏记录</h3>
                      <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  日期
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  分数
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  等级
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  时长
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {gameRecords.slice(0, 10).map((record, index) => (
                                <tr key={record._id || index} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {formatDate(record.createdAt || new Date().toISOString())}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {record.score}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {record.level || 1}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {formatDuration(record.duration || 0)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        {gameRecords.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            还没有游戏记录
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Achievements Tab */}
            {activeTab === 'achievements' && (
              <div className="space-y-8">
                {/* Achievement Progress */}
                <div className="bg-gradient-to-r from-orange-50 to-pink-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">成就进度</h3>
                    <div className="flex items-center space-x-2">
                      <Crown className="w-5 h-5 text-orange-500" />
                      <span className="text-lg font-semibold text-orange-600">
                        {unlockedAchievements.length}/{totalAchievements}
                      </span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-orange-400 to-pink-400 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${(unlockedAchievements.length / totalAchievements) * 100}%` }}
                    />
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    完成度: {Math.round((unlockedAchievements.length / totalAchievements) * 100)}%
                  </div>
                </div>

                {/* Achievement List */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {achievements.map(achievement => (
                    <div
                      key={achievement.id}
                      className={`p-6 rounded-xl border-2 transition-all ${
                        achievement.unlocked
                          ? 'bg-gradient-to-br from-green-50 to-green-100 border-green-200'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex items-start space-x-4">
                        <div className={`text-4xl ${
                          achievement.unlocked ? 'grayscale-0' : 'grayscale'
                        }`}>
                          {achievement.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className={`font-semibold mb-2 ${
                            achievement.unlocked ? 'text-green-900' : 'text-gray-500'
                          }`}>
                            {achievement.name}
                          </h4>
                          <p className={`text-sm mb-2 ${
                            achievement.unlocked ? 'text-green-700' : 'text-gray-400'
                          }`}>
                            {achievement.description}
                          </p>
                          {achievement.unlocked && achievement.unlockedAt && (
                            <div className="flex items-center space-x-1 text-xs text-green-600">
                              <Award className="w-3 h-3" />
                              <span>解锁于 {formatDate(achievement.unlockedAt)}</span>
                            </div>
                          )}
                        </div>
                        {achievement.unlocked && (
                          <div className="text-green-500">
                            <Zap className="w-5 h-5" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <div className="space-y-8">
                {isEditing ? (
                  <form onSubmit={handleEditSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Input
                        label="昵称"
                        type="text"
                        value={editForm.nickname}
                        onChange={handleInputChange('nickname')}
                        placeholder="请输入昵称"
                        fullWidth
                      />
                      <Input
                        label="邮箱"
                        type="email"
                        value={editForm.email}
                        onChange={handleInputChange('email')}
                        placeholder="请输入邮箱"
                        fullWidth
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          喜爱表情
                        </label>
                        <select
                          value={editForm.favoriteEmoji}
                          onChange={handleInputChange('favoriteEmoji')}
                          className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        >
                          <option value="😊">😊 开心</option>
                          <option value="😎">😎 酷炫</option>
                          <option value="🤔">🤔 思考</option>
                          <option value="😴">😴 困倦</option>
                          <option value="🎯">🎯 专注</option>
                          <option value="🚀">🚀 冲刺</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          主题
                        </label>
                        <select
                          value={editForm.theme}
                          onChange={handleInputChange('theme')}
                          className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        >
                          <option value="light">浅色主题</option>
                          <option value="dark">深色主题</option>
                          <option value="auto">跟随系统</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="flex space-x-4">
                      <Button type="submit" loading={isLoading}>
                        <Save className="w-4 h-4 mr-2" />
                        保存更改
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsEditing(false)}
                      >
                        取消
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-6">
                    <div className="bg-white border border-gray-200 rounded-xl p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">个人信息</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">昵称</span>
                          <span className="font-medium">{user.nickname || '未设置'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">邮箱</span>
                          <span className="font-medium">{user.email}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">用户名</span>
                          <span className="font-medium">@{user.username}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">喜爱表情</span>
                          <span className="text-2xl">{user.preferences?.favoriteEmoji || '😊'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">主题</span>
                          <span className="font-medium">
                            {user.preferences?.theme === 'dark' ? '深色主题' : 
                             user.preferences?.theme === 'auto' ? '跟随系统' : '浅色主题'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-xl p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">账户设置</h3>
                      <div className="space-y-4">
                        <Button variant="outline" fullWidth>
                          修改密码
                        </Button>
                        <Button variant="outline" fullWidth>
                          绑定社交账户
                        </Button>
                        <Button variant="outline" fullWidth>
                          数据导出
                        </Button>
                        <Button variant="outline" fullWidth className="text-red-600 border-red-200 hover:bg-red-50">
                          删除账户
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;