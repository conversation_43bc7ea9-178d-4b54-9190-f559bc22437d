/**
 * Game API routes for 工位摸鱼生存战
 */
import { Router, type Response } from 'express';
import GameRecord from '../models/GameRecord.js';
import User from '../models/User.js';
import { authMiddleware, AuthRequest } from '../utils/jwt.js';

const router = Router();

/**
 * Save game record
 * POST /api/game/record
 */
router.post('/record', authMiddleware, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { gameType, score, duration, level, achievements, gameData } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
      return;
    }

    // Validation
    if (!gameType || score === undefined || duration === undefined) {
      res.status(400).json({
        success: false,
        error: 'Game type, score and duration are required'
      });
      return;
    }

    // Create game record
    const gameRecord = new GameRecord({
      userId,
      gameType,
      score,
      duration,
      level: level || 1,
      achievements: achievements || [],
      gameData: gameData || {}
    });

    await gameRecord.save();

    // Update user game stats
    const user = await User.findById(userId);
    if (user) {
      user.gameStats.totalScore += score;
      user.gameStats.gamesPlayed += 1;
      
      if (score > user.gameStats.highestScore) {
        user.gameStats.highestScore = score;
      }

      // Add new achievements
      if (achievements && achievements.length > 0) {
        achievements.forEach((achievement: string) => {
          if (!user.gameStats.achievements.includes(achievement)) {
            user.gameStats.achievements.push(achievement);
          }
        });
      }

      await user.save();
    }

    res.status(201).json({
      success: true,
      message: 'Game record saved successfully',
      data: {
        gameRecord: {
          id: gameRecord._id,
          gameType: gameRecord.gameType,
          score: gameRecord.score,
          duration: gameRecord.duration,
          level: gameRecord.level,
          achievements: gameRecord.achievements,
          createdAt: gameRecord.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Save game record error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get user game records
 * GET /api/game/records
 */
router.get('/records', authMiddleware, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    const { gameType, limit = 10, page = 1 } = req.query;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
      return;
    }

    const query: any = { userId };
    if (gameType) {
      query.gameType = gameType;
    }

    const skip = (Number(page) - 1) * Number(limit);
    
    const records = await GameRecord.find(query)
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip(skip)
      .select('-gameData'); // Exclude detailed game data for list view

    const total = await GameRecord.countDocuments(query);

    res.json({
      success: true,
      data: {
        records,
        pagination: {
          total,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get game records error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get leaderboard
 * GET /api/game/leaderboard
 */
router.get('/leaderboard', async (req, res: Response): Promise<void> => {
  try {
    const { gameType = 'survival', limit = 10 } = req.query;

    // Get top scores for the game type
    const topRecords = await GameRecord.aggregate([
      { $match: { gameType } },
      {
        $group: {
          _id: '$userId',
          highestScore: { $max: '$score' },
          totalGames: { $sum: 1 },
          lastPlayed: { $max: '$createdAt' }
        }
      },
      { $sort: { highestScore: -1 } },
      { $limit: Number(limit) },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          _id: 0,
          userId: '$_id',
          username: '$user.username',
          nickname: '$user.nickname',
          avatar: '$user.avatar',
          highestScore: 1,
          totalGames: 1,
          lastPlayed: 1
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        leaderboard: topRecords,
        gameType
      }
    });
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get game statistics
 * GET /api/game/stats
 */
router.get('/stats', authMiddleware, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
      return;
    }

    // Get user's game statistics
    const stats = await GameRecord.aggregate([
      { $match: { userId: userId } },
      {
        $group: {
          _id: '$gameType',
          totalGames: { $sum: 1 },
          totalScore: { $sum: '$score' },
          highestScore: { $max: '$score' },
          averageScore: { $avg: '$score' },
          totalDuration: { $sum: '$duration' },
          averageDuration: { $avg: '$duration' }
        }
      }
    ]);

    // Get user info
    const user = await User.findById(userId).select('gameStats');

    res.json({
      success: true,
      data: {
        gameTypeStats: stats,
        overallStats: user?.gameStats || {
          totalScore: 0,
          highestScore: 0,
          gamesPlayed: 0,
          unlockedExpressions: [],
          achievements: []
        }
      }
    });
  } catch (error) {
    console.error('Get game stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;